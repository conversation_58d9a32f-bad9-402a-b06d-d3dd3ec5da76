{"name": "@types/puppeteer", "version": "5.4.7", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "95aedcc8474e6ac01555a79f35a738e9dca706cf12f79f03742b1352f6fb5989", "typeScriptVersion": "4.1"}